{"version": 3, "targets": {"net8.0": {"coverlet.collector/6.0.0": {"type": "package", "build": {"build/netstandard1.0/coverlet.collector.targets": {}}}, "Microsoft.CodeCoverage/17.8.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard2.0/Microsoft.CodeCoverage.props": {}, "build/netstandard2.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.NET.Test.Sdk/17.8.0": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "17.8.0", "Microsoft.TestPlatform.TestHost": "17.8.0"}, "compile": {"lib/netcoreapp3.1/_._": {}}, "runtime": {"lib/netcoreapp3.1/_._": {}}, "build": {"build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props": {}, "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.TestPlatform.ObjectModel/17.8.0": {"type": "package", "dependencies": {"NuGet.Frameworks": "6.5.0", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.8.0": {"type": "package", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.8.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "MSTest.TestAdapter/3.1.1": {"type": "package", "build": {"build/net6.0/MSTest.TestAdapter.props": {}, "build/net6.0/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/3.1.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/net6.0/MSTest.TestFramework.targets": {}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NuGet.Frameworks/6.5.0": {"type": "package", "compile": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}, "runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Reflection.Metadata/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "DAL/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"System.Data.SqlClient": "4.8.6"}, "compile": {"bin/placeholder/DAL.dll": {}}, "runtime": {"bin/placeholder/DAL.dll": {}}}, "GoodMent/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"DAL": "1.0.0", "Model": "1.0.0", "System.Data.SqlClient": "4.8.6"}, "compile": {"bin/placeholder/GoodMent.dll": {}}, "runtime": {"bin/placeholder/GoodMent.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Model/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"System.Data.SqlClient": "4.8.6"}, "compile": {"bin/placeholder/Model.dll": {}}, "runtime": {"bin/placeholder/Model.dll": {}}}}}, "libraries": {"coverlet.collector/6.0.0": {"sha512": "tW3lsNS+dAEII6YGUX/VMoJjBS1QvsxqJeqLaJXub08y1FSjasFPtQ4UBUsudE9PNrzLjooClMsPtY2cZLdXpQ==", "type": "package", "path": "coverlet.collector/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard1.0/Microsoft.Bcl.AsyncInterfaces.dll", "build/netstandard1.0/Microsoft.CSharp.dll", "build/netstandard1.0/Microsoft.DotNet.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyModel.dll", "build/netstandard1.0/Microsoft.Extensions.FileSystemGlobbing.dll", "build/netstandard1.0/Microsoft.TestPlatform.CoreUtilities.dll", "build/netstandard1.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "build/netstandard1.0/Mono.Cecil.Mdb.dll", "build/netstandard1.0/Mono.Cecil.Pdb.dll", "build/netstandard1.0/Mono.Cecil.Rocks.dll", "build/netstandard1.0/Mono.Cecil.dll", "build/netstandard1.0/Newtonsoft.Json.dll", "build/netstandard1.0/NuGet.Frameworks.dll", "build/netstandard1.0/System.AppContext.dll", "build/netstandard1.0/System.Collections.Immutable.dll", "build/netstandard1.0/System.Dynamic.Runtime.dll", "build/netstandard1.0/System.IO.FileSystem.Primitives.dll", "build/netstandard1.0/System.Linq.Expressions.dll", "build/netstandard1.0/System.Linq.dll", "build/netstandard1.0/System.ObjectModel.dll", "build/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "build/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "build/netstandard1.0/System.Reflection.Emit.dll", "build/netstandard1.0/System.Reflection.Metadata.dll", "build/netstandard1.0/System.Reflection.TypeExtensions.dll", "build/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "build/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "build/netstandard1.0/System.Text.RegularExpressions.dll", "build/netstandard1.0/System.Threading.Tasks.Extensions.dll", "build/netstandard1.0/System.Threading.dll", "build/netstandard1.0/System.Xml.ReaderWriter.dll", "build/netstandard1.0/System.Xml.XDocument.dll", "build/netstandard1.0/coverlet.collector.deps.json", "build/netstandard1.0/coverlet.collector.dll", "build/netstandard1.0/coverlet.collector.pdb", "build/netstandard1.0/coverlet.collector.targets", "build/netstandard1.0/coverlet.core.dll", "build/netstandard1.0/coverlet.core.pdb", "coverlet-icon.png", "coverlet.collector.6.0.0.nupkg.sha512", "coverlet.collector.nuspec"]}, "Microsoft.CodeCoverage/17.8.0": {"sha512": "KC8SXWbGIdoFVdlxKk9WHccm0llm9HypcHMLUUFabRiTS3SO2fQXNZfdiF3qkEdTJhbRrxhdRxjL4jbtwPq4Ew==", "type": "package", "path": "microsoft.codecoverage/17.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_MIT.txt", "ThirdPartyNotices.txt", "build/netstandard2.0/CodeCoverage/CodeCoverage.config", "build/netstandard2.0/CodeCoverage/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/VanguardInstrumentationProfiler_x86.config", "build/netstandard2.0/CodeCoverage/amd64/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/amd64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard2.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard2.0/CodeCoverage/arm64/VanguardInstrumentationProfiler_arm64.config", "build/netstandard2.0/CodeCoverage/arm64/covrunarm64.dll", "build/netstandard2.0/CodeCoverage/arm64/msdia140.dll", "build/netstandard2.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard2.0/CodeCoverage/coreclr/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "build/netstandard2.0/CodeCoverage/covrun32.dll", "build/netstandard2.0/CodeCoverage/msdia140.dll", "build/netstandard2.0/InstrumentationEngine/alpine/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/arm64/MicrosoftInstrumentationEngine_arm64.dll", "build/netstandard2.0/InstrumentationEngine/macos/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/macos/x64/libCoverageInstrumentationMethod.dylib", "build/netstandard2.0/InstrumentationEngine/macos/x64/libInstrumentationEngine.dylib", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/x64/MicrosoftInstrumentationEngine_x64.dll", "build/netstandard2.0/InstrumentationEngine/x86/MicrosoftInstrumentationEngine_x86.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Interprocess.dll", "build/netstandard2.0/Microsoft.CodeCoverage.props", "build/netstandard2.0/Microsoft.CodeCoverage.targets", "build/netstandard2.0/Microsoft.DiaSymReader.dll", "build/netstandard2.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/ThirdPartyNotices.txt", "build/netstandard2.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.17.8.0.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.NET.Test.Sdk/17.8.0": {"sha512": "BmTYGbD/YuDHmApIENdoyN1jCk0Rj1fJB0+B/fVekyTdVidr91IlzhqzytiUgaEAzL1ZJcYCme0MeBMYvJVzvw==", "type": "package", "path": "microsoft.net.test.sdk/17.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_MIT.txt", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/netcoreapp3.1/_._", "microsoft.net.test.sdk.17.8.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.TestPlatform.ObjectModel/17.8.0": {"sha512": "AYy6vlpGMfz5kOFq99L93RGbqftW/8eQTqjT9iGXW6s9MRP3UdtY8idJ8rJcjeSja8A18IhIro5YnH3uv1nz4g==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_MIT.txt", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.8.0.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "Microsoft.TestPlatform.TestHost/17.8.0": {"sha512": "9ivcl/7SGRmOT0YYrHQGohWiT5YCpkmy/UEzldfVisLm6QxbLaK3FAJqZXI34rnRLmqqDCeMQxKINwmKwAPiDw==", "type": "package", "path": "microsoft.testplatform.testhost/17.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_MIT.txt", "ThirdPartyNotices.txt", "build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props", "build/netcoreapp3.1/x64/testhost.dll", "build/netcoreapp3.1/x64/testhost.exe", "build/netcoreapp3.1/x86/testhost.x86.dll", "build/netcoreapp3.1/x86/testhost.x86.exe", "lib/net462/_._", "lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/testhost.deps.json", "lib/netcoreapp3.1/testhost.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/x64/msdia140.dll", "lib/netcoreapp3.1/x86/msdia140.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "microsoft.testplatform.testhost.17.8.0.nupkg.sha512", "microsoft.testplatform.testhost.nuspec"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "MSTest.TestAdapter/3.1.1": {"sha512": "kMvdO5dhrUR3o1qk0fzS0St0prlKyMQAfz1ChVAUdGGobTU5ehR60szOFto0+Q7rFG5iXMvTlVIthXM9EcNYnw==", "type": "package", "path": "mstest.testadapter/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/_localization/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/net462/MSTest.TestAdapter.props", "build/net462/MSTest.TestAdapter.targets", "build/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/MSTest.TestAdapter.props", "build/net6.0/MSTest.TestAdapter.targets", "build/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp3.1/MSTest.TestAdapter.props", "build/netcoreapp3.1/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netstandard2.0/MSTest.TestAdapter.props", "build/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "mstest.testadapter.3.1.1.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/3.1.1": {"sha512": "3rjkGxciNHHmPW8cl1/QVIYjOpfptjmAH5JrLBw+dnMTYDoweg3I579N7OIbar3Zd3q9dfWFrCy2LEV/AmPn3A==", "type": "package", "path": "mstest.testframework/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net6.0/MSTest.TestFramework.targets", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "mstest.testframework.3.1.1.nupkg.sha512", "mstest.testframework.nuspec"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NuGet.Frameworks/6.5.0": {"sha512": "QWINE2x3MbTODsWT1Gh71GaGb5icBz4chS8VYvTgsBnsi8esgN6wtHhydd7fvToWECYGq7T4cgBBDiKD/363fg==", "type": "package", "path": "nuget.frameworks/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net472/NuGet.Frameworks.dll", "lib/netstandard2.0/NuGet.Frameworks.dll", "nuget.frameworks.6.5.0.nupkg.sha512", "nuget.frameworks.nuspec"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/1.6.0": {"sha512": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "type": "package", "path": "system.reflection.metadata/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.1.6.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "DAL/1.0.0": {"type": "project", "path": "../DAL/DAL.csproj", "msbuildProject": "../DAL/DAL.csproj"}, "GoodMent/1.0.0": {"type": "project", "path": "../GoodMent/GoodMent.csproj", "msbuildProject": "../GoodMent/GoodMent.csproj"}, "Model/1.0.0": {"type": "project", "path": "../Model/Model.csproj", "msbuildProject": "../Model/Model.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["GoodMent >= 1.0.0", "MSTest.TestAdapter >= 3.1.1", "MSTest.TestFramework >= 3.1.1", "Microsoft.NET.Test.Sdk >= 17.8.0", "coverlet.collector >= 6.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\DevExpress\\Components\\Offline Packages": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\综合\\专业3\\综合一\\GoodMent\\TestProject1\\TestProject1.csproj", "projectName": "TestProject1", "projectPath": "C:\\Users\\<USER>\\Desktop\\综合\\专业3\\综合一\\GoodMent\\TestProject1\\TestProject1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\综合\\专业3\\综合一\\GoodMent\\TestProject1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\DevExpress\\Components\\Offline Packages", "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\DevExpress\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\综合\\专业3\\综合一\\GoodMent\\GoodMent\\GoodMent.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\综合\\专业3\\综合一\\GoodMent\\GoodMent\\GoodMent.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[3.1.1, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}