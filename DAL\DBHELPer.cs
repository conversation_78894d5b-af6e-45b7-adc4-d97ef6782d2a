﻿using System.Data;
using System.Data.SqlClient;

namespace DAL
{
    public class DBHELPer
    {

        /// <summary>
        /// 增、删、改（无参）
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="arr"></param>
        /// <returns></returns>
        public int ExecNonQuery(string sql, SqlParameter[] arr)
        {
            using (SqlConnection con = new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                con.Open();
                SqlCommand cmd = new SqlCommand(sql,con);
                cmd.CommandType=CommandType.StoredProcedure;
                cmd.Parameters.AddRange(arr);
                return cmd.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 查询无参：绑定、全部数据显示
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable Select(string sql)
        {
            using (SqlConnection con = new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                con.Open();
                SqlCommand cmd=new SqlCommand(sql,con);
                cmd.CommandType = CommandType.StoredProcedure;
                SqlDataAdapter ad=new SqlDataAdapter(cmd);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }

        /// <summary>
        /// 查询（有参）：反填，详情
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="arr"></param>
        /// <returns></returns>
        public DataTable SelById(string sql, SqlParameter[] arr)
        {
            using (SqlConnection con = new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                con.Open();
                SqlCommand cmd = new SqlCommand(sql, con);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddRange(arr);
                SqlDataAdapter ad = new SqlDataAdapter(cmd);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }

        /// <summary>
        /// 条件查询
        /// </summary>
        /// <param name="Sendname"></param>
        /// <param name="cid"></param>
        /// <returns></returns>
        public DataTable SelWhere(string Sendname,int cid)
        {
            string sql = "select * from CityInfor a join UserInfor b on a.Cid=b.Postcityid where 1=1";
            //判断文本框
            if(Sendname!="")
            {
                sql += $"and Sendname like '%{Sendname}%' ";
            }
            //判断下拉框
            if(cid!=-1)
            {
                sql += $"and Sendcityid={cid}";
            }

            using (SqlConnection con = new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                con.Open();
                
                SqlDataAdapter ad = new SqlDataAdapter(sql,con);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }

        /// <summary>
        /// 增删改（无参）
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="arr"></param>
        /// <returns></returns>
        public int ExecNonQuerys(string sql, SqlParameter[] arr)
        {
            using(SqlConnection conn=new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                conn.Open();
                SqlCommand cmd=new SqlCommand (sql, conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddRange (arr);
                return cmd.ExecuteNonQuery();
            }
        }
        /// <summary>
        /// 下拉框显示
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable Sel(string sql)
        {
            using(SqlConnection conn=new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                conn.Open ();
                SqlCommand cmd=new SqlCommand (sql, conn);
                cmd.CommandType= CommandType.StoredProcedure;
                SqlDataAdapter ad=new SqlDataAdapter(cmd);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }

        /// <summary>
        /// 反填（详情）
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="arr"></param>
        /// <returns></returns>
        public DataTable Selid(string sql, SqlParameter[] arr)
        {
            using (SqlConnection conn = new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddRange (arr);
                SqlDataAdapter ad = new SqlDataAdapter(cmd);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }

        /// <summary>
        /// 条件有参查询
        /// </summary>
        /// <param name="name"></param>
        /// <param name="tid"></param>
        /// <returns></returns>
        public DataTable Selwheres(string name,int tid)
        {
            string sql = "";

            using(SqlConnection conn=new SqlConnection("Data Source=JINXUELI\\SQL;Initial Catalog=Goodsdb;Integrated Security=True;"))
            {
                conn.Open();
                SqlDataAdapter ad = new SqlDataAdapter(sql,conn);
                DataTable dt = new DataTable();
                ad.Fill(dt);
                return dt;
            }
        }




    }
}
