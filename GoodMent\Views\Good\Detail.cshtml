﻿@using Model
@model DetailModel
@{
    Layout = null;
}

<html>
<head>
</head>
<body>
    <form asp-controller="Good" asp-action="Detail" method="post">
        <table border="1" width="80%">
            <tr>
                <td>发件人姓名</td>
                <td><input asp-for="Sendname" /></td>
                <td>用户名</td>
                <td><input asp-for="Uname" /></td>
            </tr>

            <tr>
                <td>联系方式</td>
                <td>
                    <input asp-for="Sendtel" />
                    <span asp-validation-for="Sendtel"></span>
                </td>
                <td>邮箱</td>
                <td>
                    <input asp-for="Sendemail" />
                    <span asp-validation-for="Sendemail"></span>
                </td>
            </tr>

            <tr>
                <td>城市</td>
                <td>
                    <select asp-for="Sendcityid" asp-items="ViewBag.City"></select>
                </td>
                <td>详细地址</td>
                <td>
                    <textarea asp-for="Sendaddr"></textarea>
                </td>
            </tr>

            <tr>
                <td>收件人姓名</td>
                <td><input asp-for="Postname" /></td>
                <td>下单日期</td>
                <td><input type="date" asp-for="Senddate" /></td>

            </tr>

            <tr>
                <td>收件人联系方式</td>
                <td>
                    <input asp-for="Posttel" />
                    <span asp-validation-for="Posttel"></span>
                </td>
                <td>收件人邮箱</td>
                <td>
                    <input asp-for="Postemail" />
                    <span asp-validation-for="Postemail"></span>
                </td>

            </tr>

            <tr>
                <td>城市</td>
                <td>
                    <select asp-for="Postcityid" asp-items="ViewBag.City"></select>
                </td>
                <td>收件人详细地址</td>
                <td>
                    <textarea asp-for="Postaddr"></textarea>
                </td>

            </tr>

            <tr>
                <td>商品名称</td>
                <td><input asp-for="Oname"/></td>
                <td>商品数量</td>
                <td><input asp-for="Onum" /></td>
            </tr>

            <tr>
                <td>商品单价</td>
                <td><input asp-for="Oprice" /></td>
                <td>店铺</td>
                <td><input asp-for="Odep" /></td>
            </tr>
            <tr>
                <td><input type="hidden" asp-for="Uid"/></td>
                <td><input type="button" value="返回" onclick="location.href='/Good/Show'" /></td>
                <td></td>
                <td></td>
            </tr>
        </table>
    </form>
</body>
</html>
