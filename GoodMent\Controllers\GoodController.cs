﻿using Microsoft.AspNetCore.Mvc;
using DAL;
using System.Data;
using Model;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Data.SqlClient;
using System.Text;
namespace GoodMent.Controllers
{
    public class GoodController : Controller
    {
        DBHELPer db=new DBHELPer();
        public IActionResult Index()
        {
            return View();
        }


        /// <summary>
        /// 初始页面添加
        /// </summary>
        /// <returns></returns>
        public IActionResult AddGood()
        {
            SelCity();
            return View();
        }

        /// <summary>
        /// 添加功能
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult AddGood(UserModel u)
        {
            SelCity();
            if(ModelState.IsValid)
            {
                SqlParameter[] arr =
                {
                    new SqlParameter("@Sendname",u.Sendname),
                    new SqlParameter("@Uname",u.Uname),
                    new SqlParameter("@Sendtel",u.Sendtel),
                    new SqlParameter("@Sendemail",u.Sendemail),
                    new SqlParameter("@Sendcityid",u.Sendcityid),
                    new SqlParameter("@Sendaddr",u.Sendaddr),
                    new SqlParameter("@Senddate",u.Senddate),
                    new SqlParameter("@Postname",u.Postname),
                    new SqlParameter("@Posttel",u.Posttel),
                    new SqlParameter("@Postemail",u.Postemail),
                    new SqlParameter("@Postcityid",u.Postcityid),
                    new SqlParameter("@Postaddr",u.Postaddr)
                };
                int result= db.ExecNonQuery("ProAdds",arr);
                if(result>0)
                {
                    Response.WriteAsync("<script>alert('添加成功');location.href='/Good/Show'</script>",Encoding.GetEncoding("GB2312"));
                }
                else
                {
                    Response.WriteAsync("<script>alert('添加失败')</script>", Encoding.GetEncoding("GB2312"));
                }
            }
            return View();
        }

        /// <summary>
        /// 城市下拉框绑定
        /// </summary>
        public void SelCity()
        {
            DataTable dt = db.Select("ProCity");
            List<CityModel> list = new List<CityModel>();
            foreach(DataRow a in dt.Rows)
            {
                CityModel c = new CityModel
                {
                    Cid = Convert.ToInt32(a["Cid"]),
                    Cname = a["Cname"].ToString()
                };
                list.Add(c);
            }
            ViewBag.City = new SelectList(list,"Cid","Cname");
        }

        /// <summary>
        /// 显示
        /// </summary>
        /// <returns></returns>
        public IActionResult Show()
        {
            SelCity();
            DataTable dt = db.Select("ProAll");
            List<UserModel> list= new List<UserModel>();
            foreach(DataRow a in dt.Rows)
            {
                UserModel u = new UserModel
                {
                    Uid = Convert.ToInt32(a["Uid"]),
                    Sendname = a["Sendname"].ToString(),
                    Uname= a["Uname"].ToString(),
                    Sendtel = a["Sendtel"].ToString(),
                    Sendemail= a["Sendemail"].ToString(),
                    Sendaddr = a["Sendaddr"].ToString(),
                    Cname= a["Cname"].ToString(),
                    Senddate = Convert.ToDateTime(a["Senddate"]),
                    Postname= a["Postname"].ToString(),
                    Posttel = a["Posttel"].ToString(),
                    Postaddr= a["Postaddr"].ToString(),
                };
                list.Add(u);
            }
            return View(list);
        }

        /// <summary>
        /// 条件查询
        /// </summary>
        /// <param name="Sendname"></param>
        /// <param name="cid"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Show(string Sendname,int cid)
        {
            SelCity();
            DataTable dt = db.SelWhere(Sendname,cid);
            List<UserModel> list = new List<UserModel>();
            foreach (DataRow a in dt.Rows)
            {
                UserModel u = new UserModel
                {
                    Uid = Convert.ToInt32(a["Uid"]),
                    Sendname = a["Sendname"].ToString(),
                    Uname = a["Uname"].ToString(),
                    Sendtel = a["Sendtel"].ToString(),
                    Sendemail = a["Sendemail"].ToString(),
                    Sendaddr = a["Sendaddr"].ToString(),
                    Cname = a["Cname"].ToString(),
                    Senddate = Convert.ToDateTime(a["Senddate"]),
                    Postname = a["Postname"].ToString(),
                    Posttel = a["Posttel"].ToString(),
                    Postaddr = a["Postaddr"].ToString(),
                };
                list.Add(u);
            }
            return View(list);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IActionResult Del(int id)
        {
            SqlParameter[] arr =
            {
                new SqlParameter("@Uid",id)
            };
            int r = db.ExecNonQuery("ProDel", arr);
            if (r > 0)
            {
                Response.WriteAsync("<script>alert('删除成功');location.href='/Good/Show'</script>", Encoding.GetEncoding("GB2312"));
            }
            else
            {
                Response.WriteAsync("<script>alert('删除失败')</script>", Encoding.GetEncoding("GB2312"));
            }
            return View();
        }


        /// <summary>
        /// 反填
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IActionResult Upd(int id)
        {
            SelCity();
            SqlParameter[] arr =
            {
                new SqlParameter("@Uid",id)
            };
            DataTable dt = db.SelById("ProByUid", arr);
            UserModel u=new UserModel();
            if(dt.Rows.Count > 0)
            {
                u.Uid = Convert.ToInt32(dt.Rows[0]["Uid"]);
                u.Sendname = dt.Rows[0]["Sendname"].ToString();
                u.Uname= dt.Rows[0]["Uname"].ToString();
                u.Sendtel= dt.Rows[0]["Sendtel"].ToString();
                u.Sendemail= dt.Rows[0]["Sendemail"].ToString();
                u.Sendcityid= Convert.ToInt32(dt.Rows[0]["Sendcityid"]);
                u.Sendaddr= dt.Rows[0]["Sendaddr"].ToString();
                u.Postname= dt.Rows[0]["Postname"].ToString();
                u.Senddate = Convert.ToDateTime(dt.Rows[0]["Senddate"]);
                u.Posttel= dt.Rows[0]["Posttel"].ToString();
                u.Postemail= dt.Rows[0]["Postemail"].ToString();
                u.Postcityid = Convert.ToInt32(dt.Rows[0]["Postcityid"]);
                u.Postaddr= dt.Rows[0]["Postaddr"].ToString();
            }
            return View(u);
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Upd(UserModel u)
        {
            SelCity();
            SqlParameter[] arr =
                {
                    new SqlParameter("@Sendname",u.Sendname),
                    new SqlParameter("@Uname",u.Uname),
                    new SqlParameter("@Sendtel",u.Sendtel),
                    new SqlParameter("@Sendemail",u.Sendemail),
                    new SqlParameter("@Sendcityid",u.Sendcityid),
                    new SqlParameter("@Sendaddr",u.Sendaddr),
                    new SqlParameter("@Senddate",u.Senddate),
                    new SqlParameter("@Postname",u.Postname),
                    new SqlParameter("@Posttel",u.Posttel),
                    new SqlParameter("@Postemail",u.Postemail),
                    new SqlParameter("@Postcityid",u.Postcityid),
                    new SqlParameter("@Postaddr",u.Postaddr),
                    new SqlParameter("@Uid",u.Uid)
                };
            int result = db.ExecNonQuery("ProUpd", arr);
            if (result > 0)
            {
                Response.WriteAsync("<script>alert('修改成功');location.href='/Good/Show'</script>", Encoding.GetEncoding("GB2312"));
            }
            else
            {
                Response.WriteAsync("<script>alert('修改失败')</script>", Encoding.GetEncoding("GB2312"));
            }
            return View();
        }

        /// <summary>
        /// 详情
        /// </summary>
        /// <returns></returns>
        public IActionResult Detail(int id)
        {
            SelCity();
            SqlParameter[] arr =
            {
                new SqlParameter("@Uid",id)
            };
            DataTable dt = db.SelById("ProByUid", arr);
            DetailModel u = new DetailModel();
            if (dt.Rows.Count > 0)
            {
                u.Uid = Convert.ToInt32(dt.Rows[0]["Uid"]);
                u.Sendname = dt.Rows[0]["Sendname"].ToString();
                u.Uname = dt.Rows[0]["Uname"].ToString();
                u.Sendtel = dt.Rows[0]["Sendtel"].ToString();
                u.Sendemail = dt.Rows[0]["Sendemail"].ToString();
                u.Sendcityid = Convert.ToInt32(dt.Rows[0]["Sendcityid"]);
                u.Sendaddr = dt.Rows[0]["Sendaddr"].ToString();
                u.Postname = dt.Rows[0]["Postname"].ToString();
                u.Senddate = Convert.ToDateTime(dt.Rows[0]["Senddate"]);
                u.Posttel = dt.Rows[0]["Posttel"].ToString();
                u.Postemail = dt.Rows[0]["Postemail"].ToString();
                u.Postcityid = Convert.ToInt32(dt.Rows[0]["Postcityid"]);
                u.Postaddr = dt.Rows[0]["Postaddr"].ToString();
                u.Oname= dt.Rows[0]["Oname"].ToString();
                u.Onum= Convert.ToInt32(dt.Rows[0]["Onum"]);
                u.Oprice = Convert.ToDecimal(dt.Rows[0]["Oprice"]);
                u.Odep= dt.Rows[0]["Odep"].ToString();
            }
            return View(u);
            
        }
    }
}
