﻿@using Model
@model UserModel
@{
    Layout = null;
}

<html>
    <head></head>
    <body>
        <form asp-controller="Good" asp-action="AddGood" method="post">
            <table border="1" width="80%">
                <tr>
                    <td>发件人姓名</td>
                    <td><input asp-for="Sendname"/></td>
                    <td>用户名</td>
                    <td><input asp-for="Uname"/></td>
                </tr>

                <tr>
                    <td>联系方式</td>
                    <td><input asp-for="Sendtel"/>
                    <span asp-validation-for="Sendtel"></span>    
                    </td>
                    <td>邮箱</td>
                    <td><input asp-for="Sendemail"/>
                    <span asp-validation-for="Sendemail"></span>    
                    </td>
                </tr>

                <tr>
                    <td>城市</td>
                    <td>
                    <select asp-for="Sendcityid" asp-items="ViewBag.City"></select>
                    </td>
                    <td>详细地址</td>
                    <td>
                        <textarea asp-for="Sendaddr"></textarea>
                    </td>
                </tr>

            <tr>
                <td>收件人姓名</td>
                <td><input asp-for="Postname"/></td>
                <td>下单日期</td>
                <td><input type="date" asp-for="Senddate"/></td>
                
            </tr>

            <tr>
                <td>收件人联系方式</td>
                <td>
                    <input asp-for="Posttel" />
                    <span asp-validation-for="Posttel"></span>
                </td>
                <td>收件人邮箱</td>
                <td><input asp-for="Postemail"/>
                <span asp-validation-for="Postemail"></span>    
                </td>
                
            </tr>

            <tr>
                <td>城市</td>
                <td>
                    <select asp-for="Postcityid" asp-items="ViewBag.City"></select>
                </td>
                <td>收件人详细地址</td>
                <td>
                    <textarea asp-for="Postaddr"></textarea>
                </td>
                
            </tr>

            <tr>
                <td></td>
                <td><input type="submit" value="提交"/></td>
                <td></td>
                <td><input type="reset" value="重置"/></td>
            </tr>
            </table>
        </form>
    </body>
</html>
