﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Model
{
    public class DetailModel
    {
        /// <summary>
        /// 城市编号
        /// </summary>
        public int Cid { get; set; }
        
        /// <summary>
        /// 订单编号
        /// </summary>
        public int Oid { get; set; }
        /// <summary>
        /// 用户编号
        /// </summary>
        public int Uid { get; set; }
        /// <summary>
        /// 订单名称
        /// </summary>
        public string Oname { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Onum { get; set; }
        /// <summary>
        /// 价格
        /// </summary>
        public decimal Oprice { get; set; }
        /// <summary>
        /// 店铺
        /// </summary>
        public string Odep { get; set; }
        
        /// <summary>
        /// 发件人姓名
        /// </summary>
        public string Sendname { get; set; }
        /// <summary>
        /// 用户名
        /// </summary>
        public string Uname { get; set; }
        /// <summary>
        /// 发件人电话
        /// </summary>
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式有误")]
        public string Sendtel { get; set; }
        /// <summary>
        /// 发件人邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "邮箱格式有误")]
        public string Sendemail { get; set; }
        /// <summary>
        /// 发件人城市
        /// </summary>
        public int Sendcityid { get; set; }
        /// <summary>
        /// 发件人地址
        /// </summary>
        public string Sendaddr { get; set; }
        /// <summary>
        /// 发送日期
        /// </summary>
        public DateTime Senddate { get; set; }
        /// <summary>
        /// 收件人姓名
        /// </summary>
        public string Postname { get; set; }
        /// <summary>
        /// 收件人电话
        /// </summary>
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式有误")]
        public string Posttel { get; set; }
        /// <summary>
        /// 收件人邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "邮箱格式有误")]
        public string Postemail { get; set; }
        /// <summary>
        /// 收件人城市
        /// </summary>
        public int Postcityid { get; set; }
        /// <summary>
        /// 收件地址
        /// </summary>
        public string Postaddr { get; set; }


        /// <summary>
        /// 附加属性（城市名称）
        /// </summary>
        public string? Cname { get; set; }
    }
}
