﻿@using Model
@model IEnumerable<UserModel>
@{
    Layout = null;
}
<html>
    <head></head>
    <body>
        <form asp-controller="Good" asp-action="Show" method="post">
            发件人姓名<input name="Sendname"/>
        城市<select name="cid" asp-items="ViewBag.City">
            <option value="-1">请选择</option>
        </select>
            <input type="submit" value="查询"/>
        </form>
        <table border="1" width="80%">
            <thead>
                <tr>
                    <th>编号</th>
                    <th>发件人姓名</th>
                    <th>用户名</th>
                    <th>发件人电话</th>
                    <th>发件人邮箱</th>
                    <th>详细地址</th>
                    <th>下单日期</th>
                    <th>城市</th>
                    <th>收件人姓名</th>
                    <th>收件人电话</th>
                    <th>收件人地址</th>
                    <th>操作</th>
                </tr>
            </thead>

            <tbody>
                @foreach(var a in Model)
            {
                <tr>
                    <td>@a.Uid</td>
                    <td>@a.Sendname</td>
                    <td>@a.Uname</td>
                    <td>@a.Sendtel</td>
                    <td>@a.Sendemail</td>
                    <td>@a.Sendaddr</td>
                    <td>@a.Senddate</td>
                    <td>@a.Cname</td>
                    <td>@a.Postname</td>
                    <td>@a.Posttel</td>
                    <td>@a.Postaddr</td>
                    <td>
                        <a asp-controller="Good" asp-action="Del" asp-route-id="@a.Uid" onclick="return confirm('确认取消吗？')">取消</a>
                        <a asp-controller="Good" asp-action="Upd" asp-route-id="@a.Uid" onclick="return confirm('确认修改吗？')">修改</a>
                        <a asp-controller="Good" asp-action="Detail" asp-route-id="@a.Uid">详情</a>
                    </td>
                </tr>
            }
        </tbody>
        </table>
    </body>
</html>
